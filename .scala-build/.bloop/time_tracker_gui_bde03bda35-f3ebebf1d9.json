{"version": "1.4.0", "project": {"name": "time_tracker_gui_bde03bda35-f3ebebf1d9", "directory": "/tmp/time_tracker_gui/.scala-build", "workspaceDir": "/tmp/time_tracker_gui", "sources": ["/tmp/time_tracker_gui/time-tracker-enhanced-with-api.scala"], "dependencies": [], "classpath": ["/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala3-library_3/3.5.0/scala3-library_3-3.5.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/tpolecat/skunk-core_3/0.6.4/skunk-core_3-0.6.4.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/slf4j/slf4j-nop/2.0.17/slf4j-nop-2.0.17.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/github/alexarchambault/case-app_3/2.1.0-M30/case-app_3-2.1.0-M30.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/os-lib_3/0.11.4/os-lib_3-0.11.4.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-ember-server_3/0.23.30/http4s-ember-server_3-0.23.30.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-ember-client_3/0.23.30/http4s-ember-client_3-0.23.30.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-circe_3/0.23.30/http4s-circe_3-0.23.30.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-dsl_3/0.23.30/http4s-dsl_3-0.23.30.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-core_3/0.14.13/circe-core_3-0.14.13.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-generic_3/0.14.13/circe-generic_3-0.14.13.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-parser_3/0.14.13/circe-parser_3-0.14.13.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-jdk-http-client_3/0.10.0/http4s-jdk-http-client_3-0.10.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/comcast/ip4s-core_3/3.7.0/ip4s-core_3-3.7.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.14/scala-library-2.13.14.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-core_3/2.13.0/cats-core_3-2.13.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect_3/3.6.1/cats-effect_3-3.6.1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/co/fs2/fs2-core_3/3.11.0/fs2-core_3-3.11.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/co/fs2/fs2-io_3/3.11.0/fs2-io_3-3.11.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scodec/scodec-bits_3/1.2.1/scodec-bits_3-1.2.1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scodec/scodec-core_3/2.2.2/scodec-core_3-2.2.2.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scodec/scodec-cats_3/1.2.0/scodec-cats_3-1.2.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/tpolecat/natchez-core_3/0.3.5/natchez-core_3-0.3.5.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/tpolecat/sourcepos_3/1.1.0/sourcepos_3-1.1.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_3/2.11.0/scala-collection-compat_3-2.11.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/twiddles-core_3/0.6.2/twiddles-core_3-0.6.2.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/ongres/scram/client/2.1/client-2.1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/sourcecode_3/0.4.2/sourcecode_3-0.4.2.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/github/alexarchambault/case-app-annotations_3/2.1.0-M30/case-app-annotations_3-2.1.0-M30.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/github/alexarchambault/case-app-util_3/2.1.0-M30/case-app-util_3-2.1.0-M30.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/geny_3/1.1.1/geny_3-1.1.1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-ember-core_3/0.23.30/http4s-ember-core_3-0.23.30.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-server_3/0.23.30/http4s-server_3-0.23.30.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/log4cats-slf4j_3/2.7.0/log4cats-slf4j_3-2.7.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-client_3/0.23.30/http4s-client_3-0.23.30.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/keypool_3/0.4.10/keypool_3-0.4.10.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-core_3/0.23.30/http4s-core_3-0.23.30.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-jawn_3/0.23.30/http4s-jawn_3-0.23.30.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-jawn_3/0.14.13/circe-jawn_3-0.14.13.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-numbers_3/0.14.13/circe-numbers_3-0.14.13.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect-kernel_3/3.6.1/cats-effect-kernel_3-3.6.1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect-std_3/3.6.1/cats-effect-std_3-3.6.1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/vault_3/3.6.0/vault_3-3.6.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/case-insensitive_3/1.4.2/case-insensitive_3-1.4.2.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/literally_3/1.1.0/literally_3-1.1.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-kernel_3/2.13.0/cats-kernel_3-2.13.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-mtl_3/1.3.1/cats-mtl_3-1.3.1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/ongres/scram/common/2.1/common-2.1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/log4cats-core_3/2.7.0/log4cats-core_3-2.7.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/twitter/hpack/1.0.2/hpack-1.0.2.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-parse_3/1.0.0/cats-parse_3-1.0.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-crypto_3/0.2.4/http4s-crypto_3-0.2.4.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/log4s/log4s_3/1.10.0/log4s_3-1.10.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/jawn-fs2_3/2.4.0/jawn-fs2_3-2.4.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/jawn-parser_3/1.6.0/jawn-parser_3-1.6.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/ongres/stringprep/saslprep/1.1/saslprep-1.1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/ongres/stringprep/stringprep/1.1/stringprep-1.1.jar"], "out": "/tmp/time_tracker_gui/.scala-build/.bloop/time_tracker_gui_bde03bda35-f3ebebf1d9", "classesDir": "/tmp/time_tracker_gui/.scala-build/time_tracker_gui_bde03bda35-f3ebebf1d9/classes/main", "scala": {"organization": "org.scala-lang", "name": "scala-compiler", "version": "3.5.0", "options": ["-sourceroot", "/tmp/time_tracker_gui"], "jars": ["/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala3-compiler_3/3.5.0/scala3-compiler_3-3.5.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala3-interfaces/3.5.0/scala3-interfaces-3.5.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala3-library_3/3.5.0/scala3-library_3-3.5.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/tasty-core_3/3.5.0/tasty-core_3-3.5.0.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-asm/9.6.0-scala-1/scala-asm-9.6.0-scala-1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/compiler-interface/1.9.6/compiler-interface-1.9.6.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-reader/3.25.1/jline-reader-3.25.1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-terminal/3.25.1/jline-terminal-3.25.1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-terminal-jna/3.25.1/jline-terminal-jna-3.25.1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.14/scala-library-2.13.14.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-interface/1.9.8/util-interface-1.9.8.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-native/3.25.1/jline-native-3.25.1.jar", "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/net/java/dev/jna/jna/5.14.0/jna-5.14.0.jar"], "bridgeJars": ["/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala3-sbt-bridge/3.5.0/scala3-sbt-bridge-3.5.0.jar"]}, "java": {"options": []}, "platform": {"name": "jvm", "config": {"home": "/home/<USER>/.cache/coursier/arc/https/github.com/adoptium/temurin17-binaries/releases/download/jdk-17.0.15%252B6/OpenJDK17U-jdk_x64_linux_hotspot_17.0.15_6.tar.gz/jdk-17.0.15+6", "options": []}, "mainClass": []}, "resolution": {"modules": [{"organization": "org.scala-lang", "name": "scala3-library_3", "version": "3.5.0", "artifacts": [{"name": "scala3-library_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala3-library_3/3.5.0/scala3-library_3-3.5.0.jar"}]}, {"organization": "org.tpolecat", "name": "skunk-core_3", "version": "0.6.4", "artifacts": [{"name": "skunk-core_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/tpolecat/skunk-core_3/0.6.4/skunk-core_3-0.6.4.jar"}]}, {"organization": "org.slf4j", "name": "slf4j-nop", "version": "2.0.17", "artifacts": [{"name": "slf4j-nop", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/slf4j/slf4j-nop/2.0.17/slf4j-nop-2.0.17.jar"}]}, {"organization": "com.github.alexarchambault", "name": "case-app_3", "version": "2.1.0-M30", "artifacts": [{"name": "case-app_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/github/alexarchambault/case-app_3/2.1.0-M30/case-app_3-2.1.0-M30.jar"}]}, {"organization": "com.lihaoyi", "name": "os-lib_3", "version": "0.11.4", "artifacts": [{"name": "os-lib_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/os-lib_3/0.11.4/os-lib_3-0.11.4.jar"}]}, {"organization": "org.http4s", "name": "http4s-ember-server_3", "version": "0.23.30", "artifacts": [{"name": "http4s-ember-server_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-ember-server_3/0.23.30/http4s-ember-server_3-0.23.30.jar"}]}, {"organization": "org.http4s", "name": "http4s-ember-client_3", "version": "0.23.30", "artifacts": [{"name": "http4s-ember-client_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-ember-client_3/0.23.30/http4s-ember-client_3-0.23.30.jar"}]}, {"organization": "org.http4s", "name": "http4s-circe_3", "version": "0.23.30", "artifacts": [{"name": "http4s-circe_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-circe_3/0.23.30/http4s-circe_3-0.23.30.jar"}]}, {"organization": "org.http4s", "name": "http4s-dsl_3", "version": "0.23.30", "artifacts": [{"name": "http4s-dsl_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-dsl_3/0.23.30/http4s-dsl_3-0.23.30.jar"}]}, {"organization": "io.circe", "name": "circe-core_3", "version": "0.14.13", "artifacts": [{"name": "circe-core_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-core_3/0.14.13/circe-core_3-0.14.13.jar"}]}, {"organization": "io.circe", "name": "circe-generic_3", "version": "0.14.13", "artifacts": [{"name": "circe-generic_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-generic_3/0.14.13/circe-generic_3-0.14.13.jar"}]}, {"organization": "io.circe", "name": "circe-parser_3", "version": "0.14.13", "artifacts": [{"name": "circe-parser_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-parser_3/0.14.13/circe-parser_3-0.14.13.jar"}]}, {"organization": "org.http4s", "name": "http4s-jdk-http-client_3", "version": "0.10.0", "artifacts": [{"name": "http4s-jdk-http-client_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-jdk-http-client_3/0.10.0/http4s-jdk-http-client_3-0.10.0.jar"}]}, {"organization": "com.comcast", "name": "ip4s-core_3", "version": "3.7.0", "artifacts": [{"name": "ip4s-core_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/comcast/ip4s-core_3/3.7.0/ip4s-core_3-3.7.0.jar"}]}, {"organization": "org.scala-lang", "name": "scala-library", "version": "2.13.14", "artifacts": [{"name": "scala-library", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.14/scala-library-2.13.14.jar"}]}, {"organization": "org.typelevel", "name": "cats-core_3", "version": "2.13.0", "artifacts": [{"name": "cats-core_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-core_3/2.13.0/cats-core_3-2.13.0.jar"}]}, {"organization": "org.typelevel", "name": "cats-effect_3", "version": "3.6.1", "artifacts": [{"name": "cats-effect_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect_3/3.6.1/cats-effect_3-3.6.1.jar"}]}, {"organization": "co.fs2", "name": "fs2-core_3", "version": "3.11.0", "artifacts": [{"name": "fs2-core_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/co/fs2/fs2-core_3/3.11.0/fs2-core_3-3.11.0.jar"}]}, {"organization": "co.fs2", "name": "fs2-io_3", "version": "3.11.0", "artifacts": [{"name": "fs2-io_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/co/fs2/fs2-io_3/3.11.0/fs2-io_3-3.11.0.jar"}]}, {"organization": "org.scodec", "name": "scodec-bits_3", "version": "1.2.1", "artifacts": [{"name": "scodec-bits_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scodec/scodec-bits_3/1.2.1/scodec-bits_3-1.2.1.jar"}]}, {"organization": "org.scodec", "name": "scodec-core_3", "version": "2.2.2", "artifacts": [{"name": "scodec-core_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scodec/scodec-core_3/2.2.2/scodec-core_3-2.2.2.jar"}]}, {"organization": "org.scodec", "name": "scodec-cats_3", "version": "1.2.0", "artifacts": [{"name": "scodec-cats_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scodec/scodec-cats_3/1.2.0/scodec-cats_3-1.2.0.jar"}]}, {"organization": "org.tpolecat", "name": "natchez-core_3", "version": "0.3.5", "artifacts": [{"name": "natchez-core_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/tpolecat/natchez-core_3/0.3.5/natchez-core_3-0.3.5.jar"}]}, {"organization": "org.tpolecat", "name": "sourcepos_3", "version": "1.1.0", "artifacts": [{"name": "sourcepos_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/tpolecat/sourcepos_3/1.1.0/sourcepos_3-1.1.0.jar"}]}, {"organization": "org.scala-lang.modules", "name": "scala-collection-compat_3", "version": "2.11.0", "artifacts": [{"name": "scala-collection-compat_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_3/2.11.0/scala-collection-compat_3-2.11.0.jar"}]}, {"organization": "org.typelevel", "name": "twiddles-core_3", "version": "0.6.2", "artifacts": [{"name": "twiddles-core_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/twiddles-core_3/0.6.2/twiddles-core_3-0.6.2.jar"}]}, {"organization": "com.ongres.scram", "name": "client", "version": "2.1", "artifacts": [{"name": "client", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/ongres/scram/client/2.1/client-2.1.jar"}]}, {"organization": "org.slf4j", "name": "slf4j-api", "version": "2.0.17", "artifacts": [{"name": "slf4j-api", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar"}]}, {"organization": "com.lihaoyi", "name": "sourcecode_3", "version": "0.4.2", "artifacts": [{"name": "sourcecode_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/sourcecode_3/0.4.2/sourcecode_3-0.4.2.jar"}]}, {"organization": "com.github.alexarchambault", "name": "case-app-annotations_3", "version": "2.1.0-M30", "artifacts": [{"name": "case-app-annotations_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/github/alexarchambault/case-app-annotations_3/2.1.0-M30/case-app-annotations_3-2.1.0-M30.jar"}]}, {"organization": "com.github.alexarchambault", "name": "case-app-util_3", "version": "2.1.0-M30", "artifacts": [{"name": "case-app-util_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/github/alexarchambault/case-app-util_3/2.1.0-M30/case-app-util_3-2.1.0-M30.jar"}]}, {"organization": "com.lihaoyi", "name": "geny_3", "version": "1.1.1", "artifacts": [{"name": "geny_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/geny_3/1.1.1/geny_3-1.1.1.jar"}]}, {"organization": "org.http4s", "name": "http4s-ember-core_3", "version": "0.23.30", "artifacts": [{"name": "http4s-ember-core_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-ember-core_3/0.23.30/http4s-ember-core_3-0.23.30.jar"}]}, {"organization": "org.http4s", "name": "http4s-server_3", "version": "0.23.30", "artifacts": [{"name": "http4s-server_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-server_3/0.23.30/http4s-server_3-0.23.30.jar"}]}, {"organization": "org.typelevel", "name": "log4cats-slf4j_3", "version": "2.7.0", "artifacts": [{"name": "log4cats-slf4j_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/log4cats-slf4j_3/2.7.0/log4cats-slf4j_3-2.7.0.jar"}]}, {"organization": "org.http4s", "name": "http4s-client_3", "version": "0.23.30", "artifacts": [{"name": "http4s-client_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-client_3/0.23.30/http4s-client_3-0.23.30.jar"}]}, {"organization": "org.typelevel", "name": "keypool_3", "version": "0.4.10", "artifacts": [{"name": "keypool_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/keypool_3/0.4.10/keypool_3-0.4.10.jar"}]}, {"organization": "org.http4s", "name": "http4s-core_3", "version": "0.23.30", "artifacts": [{"name": "http4s-core_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-core_3/0.23.30/http4s-core_3-0.23.30.jar"}]}, {"organization": "org.http4s", "name": "http4s-jawn_3", "version": "0.23.30", "artifacts": [{"name": "http4s-jawn_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-jawn_3/0.23.30/http4s-jawn_3-0.23.30.jar"}]}, {"organization": "io.circe", "name": "circe-jawn_3", "version": "0.14.13", "artifacts": [{"name": "circe-jawn_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-jawn_3/0.14.13/circe-jawn_3-0.14.13.jar"}]}, {"organization": "io.circe", "name": "circe-numbers_3", "version": "0.14.13", "artifacts": [{"name": "circe-numbers_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-numbers_3/0.14.13/circe-numbers_3-0.14.13.jar"}]}, {"organization": "org.typelevel", "name": "cats-effect-kernel_3", "version": "3.6.1", "artifacts": [{"name": "cats-effect-kernel_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect-kernel_3/3.6.1/cats-effect-kernel_3-3.6.1.jar"}]}, {"organization": "org.typelevel", "name": "cats-effect-std_3", "version": "3.6.1", "artifacts": [{"name": "cats-effect-std_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect-std_3/3.6.1/cats-effect-std_3-3.6.1.jar"}]}, {"organization": "org.typelevel", "name": "vault_3", "version": "3.6.0", "artifacts": [{"name": "vault_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/vault_3/3.6.0/vault_3-3.6.0.jar"}]}, {"organization": "org.typelevel", "name": "case-insensitive_3", "version": "1.4.2", "artifacts": [{"name": "case-insensitive_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/case-insensitive_3/1.4.2/case-insensitive_3-1.4.2.jar"}]}, {"organization": "org.typelevel", "name": "literally_3", "version": "1.1.0", "artifacts": [{"name": "literally_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/literally_3/1.1.0/literally_3-1.1.0.jar"}]}, {"organization": "org.typelevel", "name": "cats-kernel_3", "version": "2.13.0", "artifacts": [{"name": "cats-kernel_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-kernel_3/2.13.0/cats-kernel_3-2.13.0.jar"}]}, {"organization": "org.typelevel", "name": "cats-mtl_3", "version": "1.3.1", "artifacts": [{"name": "cats-mtl_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-mtl_3/1.3.1/cats-mtl_3-1.3.1.jar"}]}, {"organization": "com.ongres.scram", "name": "common", "version": "2.1", "artifacts": [{"name": "common", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/ongres/scram/common/2.1/common-2.1.jar"}]}, {"organization": "org.typelevel", "name": "log4cats-core_3", "version": "2.7.0", "artifacts": [{"name": "log4cats-core_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/log4cats-core_3/2.7.0/log4cats-core_3-2.7.0.jar"}]}, {"organization": "com.twitter", "name": "hpack", "version": "1.0.2", "artifacts": [{"name": "hpack", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/twitter/hpack/1.0.2/hpack-1.0.2.jar"}]}, {"organization": "org.typelevel", "name": "cats-parse_3", "version": "1.0.0", "artifacts": [{"name": "cats-parse_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-parse_3/1.0.0/cats-parse_3-1.0.0.jar"}]}, {"organization": "org.http4s", "name": "http4s-crypto_3", "version": "0.2.4", "artifacts": [{"name": "http4s-crypto_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/http4s/http4s-crypto_3/0.2.4/http4s-crypto_3-0.2.4.jar"}]}, {"organization": "org.log4s", "name": "log4s_3", "version": "1.10.0", "artifacts": [{"name": "log4s_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/log4s/log4s_3/1.10.0/log4s_3-1.10.0.jar"}]}, {"organization": "org.typelevel", "name": "jawn-fs2_3", "version": "2.4.0", "artifacts": [{"name": "jawn-fs2_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/jawn-fs2_3/2.4.0/jawn-fs2_3-2.4.0.jar"}]}, {"organization": "org.typelevel", "name": "jawn-parser_3", "version": "1.6.0", "artifacts": [{"name": "jawn-parser_3", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/org/typelevel/jawn-parser_3/1.6.0/jawn-parser_3-1.6.0.jar"}]}, {"organization": "com.ongres.stringprep", "name": "saslprep", "version": "1.1", "artifacts": [{"name": "saslprep", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/ongres/stringprep/saslprep/1.1/saslprep-1.1.jar"}]}, {"organization": "com.ongres.stringprep", "name": "stringprep", "version": "1.1", "artifacts": [{"name": "stringprep", "path": "/home/<USER>/.cache/coursier/v1/https/repo1.maven.org/maven2/com/ongres/stringprep/stringprep/1.1/stringprep-1.1.jar"}]}]}, "tags": ["library"]}}
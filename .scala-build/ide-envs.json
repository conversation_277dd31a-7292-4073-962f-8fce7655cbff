{"LD_LIBRARY_PATH": "/home/<USER>/.local/share/vulkan/x86_64/lib", "ZDOTDIR": "/home/<USER>/.config/zsh", "SHELL": "/usr/bin/zsh", "PATH": "/home/<USER>/.local/share/vulkan/x86_64/bin:/home/<USER>/.cargo/bin:/home/<USER>/.cache/appimage:~/Documents/repos/lit/bin:/opt/clang-format-static:/home/<USER>/.nix-profile/bin:/nix/var/nix/profiles/default/bin:/home/<USER>/.nix-profile/bin:/nix/var/nix/profiles/default/bin:/usr/local/sbin:/usr/local/bin:/usr/bin:/opt/android-sdk/emulator:/opt/android-sdk/platform-tools:/opt/android-sdk/tools:/opt/android-sdk/tools/bin:/usr/lib/emscripten:/var/lib/flatpak/exports/bin:/usr/lib/jvm/default/bin:/usr/bin/site_perl:/usr/bin/vendor_perl:/usr/bin/core_perl:/usr/lib/rustup/bin:/home/<USER>/.local/share/coursier/bin:/opt/android-sdk/emulator:/opt/android-sdk/platform-tools:/opt/android-sdk/tools:/opt/android-sdk/tools/bin:/usr/lib/emscripten:/home/<USER>/.local/share/coursier/bin:/opt/sonar-scanner/bin:/home/<USER>/.local/share/coursier/bin:/home/<USER>/.dotnet/tools"}
#!/usr/bin/env dart

import 'dart:convert';
import 'dart:io';

/// Simple test script to verify communication between Flutter app and Scala backend
/// Run this script to test the API endpoints before running the full Flutter app

const String baseUrl = 'http://localhost:8080';

Future<void> main() async {
  print('🚀 Testing Time Tracker API Communication...\n');

  final client = HttpClient();
  
  try {
    // Test 1: Get all apps
    await testGetAllApps(client);
    
    // Test 2: Get tracking status
    await testGetTrackingStatus(client);
    
    // Test 3: Add a new app
    await testAddApp(client, 'test-app-${DateTime.now().millisecondsSinceEpoch}');
    
    // Test 4: Start tracking
    await testTrackingCommand(client, 'start');
    
    // Test 5: Get statistics
    await testGetStatistics(client);
    
    // Test 6: Pause tracking
    await testTrackingCommand(client, 'pause');
    
    // Test 7: Resume tracking
    await testTrackingCommand(client, 'resume');
    
    // Test 8: Stop tracking
    await testTrackingCommand(client, 'stop');
    
    print('\n✅ All tests completed successfully!');
    print('🎉 The Scala backend is ready for Flutter app communication.');
    
  } catch (e) {
    print('\n❌ Test failed: $e');
    print('💡 Make sure the Scala backend is running on port 8080');
    print('   Run: scala-cli run time-tracker-enhanced-with-api.scala -- --server --debug');
  } finally {
    client.close();
  }
}

Future<void> testGetAllApps(HttpClient client) async {
  print('📱 Testing GET /api/apps...');
  
  final request = await client.getUrl(Uri.parse('$baseUrl/api/apps'));
  request.headers.set('Content-Type', 'application/json');
  
  final response = await request.close();
  final responseBody = await response.transform(utf8.decoder).join();
  
  if (response.statusCode == 200) {
    final data = jsonDecode(responseBody);
    print('   ✅ Success: Found ${(data['data'] as List).length} apps');
    if (data['data'].isNotEmpty) {
      print('   📋 Sample app: ${data['data'][0]['name']}');
    }
  } else {
    throw Exception('Failed to get apps: ${response.statusCode}');
  }
}

Future<void> testGetTrackingStatus(HttpClient client) async {
  print('📊 Testing GET /api/tracking/status...');
  
  final request = await client.getUrl(Uri.parse('$baseUrl/api/tracking/status'));
  request.headers.set('Content-Type', 'application/json');
  
  final response = await request.close();
  final responseBody = await response.transform(utf8.decoder).join();
  
  if (response.statusCode == 200) {
    final data = jsonDecode(responseBody);
    final status = data['data'];
    print('   ✅ Success: Tracking=${status['isTracking']}, Paused=${status['isPaused']}');
    if (status['currentApp'] != null) {
      print('   🎯 Current app: ${status['currentApp']}');
    }
  } else {
    throw Exception('Failed to get tracking status: ${response.statusCode}');
  }
}

Future<void> testAddApp(HttpClient client, String appName) async {
  print('➕ Testing POST /api/apps (adding "$appName")...');
  
  final request = await client.postUrl(Uri.parse('$baseUrl/api/apps'));
  request.headers.set('Content-Type', 'application/json');
  
  final requestBody = jsonEncode({'name': appName});
  request.write(requestBody);
  
  final response = await request.close();
  final responseBody = await response.transform(utf8.decoder).join();
  
  if (response.statusCode == 201) {
    final data = jsonDecode(responseBody);
    print('   ✅ Success: ${data['message']}');
  } else {
    throw Exception('Failed to add app: ${response.statusCode} - $responseBody');
  }
}

Future<void> testTrackingCommand(HttpClient client, String command) async {
  print('🎮 Testing POST /api/tracking/command ($command)...');
  
  final request = await client.postUrl(Uri.parse('$baseUrl/api/tracking/command'));
  request.headers.set('Content-Type', 'application/json');
  
  final requestBody = jsonEncode({'command': command});
  request.write(requestBody);
  
  final response = await request.close();
  final responseBody = await response.transform(utf8.decoder).join();
  
  if (response.statusCode == 200) {
    final data = jsonDecode(responseBody);
    print('   ✅ Success: ${data['message']}');
  } else {
    throw Exception('Failed to send command: ${response.statusCode} - $responseBody');
  }
}

Future<void> testGetStatistics(HttpClient client) async {
  print('📈 Testing GET /api/statistics...');
  
  final request = await client.getUrl(Uri.parse('$baseUrl/api/statistics'));
  request.headers.set('Content-Type', 'application/json');
  
  final response = await request.close();
  final responseBody = await response.transform(utf8.decoder).join();
  
  if (response.statusCode == 200) {
    final data = jsonDecode(responseBody);
    final stats = data['data'] as List;
    print('   ✅ Success: Generated statistics for ${stats.length} apps');
    if (stats.isNotEmpty) {
      final firstStat = stats[0];
      print('   📊 Sample: ${firstStat['app']['name']} - ${firstStat['totalDuration']} minutes');
    }
  } else {
    throw Exception('Failed to get statistics: ${response.statusCode}');
  }
}

{"name": "scala-cli", "argv": ["/usr/bin/scala-cli", "bsp", "--json-options", "/tmp/time_tracker_gui/.scala-build/ide-options-v2.json", "--json-launcher-options", "/tmp/time_tracker_gui/.scala-build/ide-launcher-options.json", "--envs-file", "/tmp/time_tracker_gui/.scala-build/ide-envs.json", "/tmp/time_tracker_gui/time-tracker-enhanced-with-api.scala"], "version": "1.5.0", "bspVersion": "2.1.1", "languages": ["scala", "java"]}
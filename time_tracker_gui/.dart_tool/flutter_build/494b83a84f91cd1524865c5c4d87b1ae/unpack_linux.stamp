{"inputs": ["/home/<USER>/.cache/flutter_sdk/packages/flutter_tools/lib/src/build_system/targets/linux.dart", "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/libflutter_linux_gtk.so", "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/icudtl.dat", "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_method_call.h", "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_dart_project.h", "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_view.h", "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_texture.h", "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_value.h", "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_standard_method_codec.h", "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_method_response.h", "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_method_codec.h", "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_message_codec.h", "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_engine.h", "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_texture_registrar.h", "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/flutter_linux.h", "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_event_channel.h", "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_string_codec.h", "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_application.h", "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_method_channel.h", "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_plugin_registrar.h", "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_plugin_registry.h", "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_json_method_codec.h", "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_binary_codec.h", "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_binary_messenger.h", "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_texture_gl.h", "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_json_message_codec.h", "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_pixel_buffer_texture.h", "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_standard_message_codec.h", "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_basic_message_channel.h"], "outputs": ["/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/libflutter_linux_gtk.so", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/icudtl.dat", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_method_call.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_dart_project.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_view.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_texture.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_value.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_standard_method_codec.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_method_response.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_method_codec.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_message_codec.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_engine.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_texture_registrar.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/flutter_linux.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_event_channel.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_string_codec.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_application.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_method_channel.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_plugin_registrar.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_plugin_registry.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_json_method_codec.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_binary_codec.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_binary_messenger.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_texture_gl.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_json_message_codec.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_pixel_buffer_texture.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_standard_message_codec.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_basic_message_channel.h"]}
["/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/libflutter_linux_gtk.so", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/icudtl.dat", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_method_call.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_dart_project.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_view.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_texture.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_value.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_standard_method_codec.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_method_response.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_method_codec.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_message_codec.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_engine.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_texture_registrar.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/flutter_linux.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_event_channel.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_string_codec.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_application.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_method_channel.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_plugin_registrar.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_plugin_registry.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_json_method_codec.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_binary_codec.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_binary_messenger.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_texture_gl.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_json_message_codec.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_pixel_buffer_texture.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_standard_message_codec.h", "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_basic_message_channel.h", "/tmp/time_tracker_gui/time_tracker_gui/build/flutter_assets/kernel_blob.bin", "/tmp/time_tracker_gui/time_tracker_gui/build/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "/tmp/time_tracker_gui/time_tracker_gui/build/flutter_assets/fonts/MaterialIcons-Regular.otf", "/tmp/time_tracker_gui/time_tracker_gui/build/flutter_assets/shaders/ink_sparkle.frag", "/tmp/time_tracker_gui/time_tracker_gui/build/flutter_assets/AssetManifest.json", "/tmp/time_tracker_gui/time_tracker_gui/build/flutter_assets/AssetManifest.bin", "/tmp/time_tracker_gui/time_tracker_gui/build/flutter_assets/FontManifest.json", "/tmp/time_tracker_gui/time_tracker_gui/build/flutter_assets/NOTICES.Z", "/tmp/time_tracker_gui/time_tracker_gui/build/flutter_assets/version.json", "/tmp/time_tracker_gui/time_tracker_gui/build/flutter_assets/NativeAssetsManifest.json"]
{"inputs": ["/tmp/time_tracker_gui/time_tracker_gui/.dart_tool/flutter_build/494b83a84f91cd1524865c5c4d87b1ae/app.dill", "/tmp/time_tracker_gui/time_tracker_gui/pubspec.yaml", "/home/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/assets/CupertinoIcons.ttf", "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf", "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/shaders/ink_sparkle.frag", "/tmp/time_tracker_gui/time_tracker_gui/.dart_tool/flutter_build/494b83a84f91cd1524865c5c4d87b1ae/native_assets.json", "/home/<USER>/.cache/flutter_sdk/bin/cache/pkg/sky_engine/LICENSE", "/home/<USER>/.cache/flutter_sdk/packages/flutter/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-82.0.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/analyzer-7.4.5/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/animations-2.0.11/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/build-2.4.2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/built_collection-5.1.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/built_value-8.9.5/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/code_builder-4.10.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/dart_style-3.1.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-5.0.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_screenutil-5.9.3/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/glob-2.1.3/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.8/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/lints-5.1.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/mockito-5.4.6/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/package_config-2.2.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/pub_semver-2.2.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/source_gen-2.0.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/state_notifier-1.0.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.3.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/watcher-1.1.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/LICENSE", "/home/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/LICENSE"], "outputs": ["/tmp/time_tracker_gui/time_tracker_gui/build/flutter_assets/kernel_blob.bin", "/tmp/time_tracker_gui/time_tracker_gui/build/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "/tmp/time_tracker_gui/time_tracker_gui/build/flutter_assets/fonts/MaterialIcons-Regular.otf", "/tmp/time_tracker_gui/time_tracker_gui/build/flutter_assets/shaders/ink_sparkle.frag", "/tmp/time_tracker_gui/time_tracker_gui/build/flutter_assets/AssetManifest.json", "/tmp/time_tracker_gui/time_tracker_gui/build/flutter_assets/AssetManifest.bin", "/tmp/time_tracker_gui/time_tracker_gui/build/flutter_assets/FontManifest.json", "/tmp/time_tracker_gui/time_tracker_gui/build/flutter_assets/NOTICES.Z", "/tmp/time_tracker_gui/time_tracker_gui/build/flutter_assets/version.json", "/tmp/time_tracker_gui/time_tracker_gui/build/flutter_assets/NativeAssetsManifest.json"]}
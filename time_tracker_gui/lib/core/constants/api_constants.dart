class ApiConstants {
  static const String baseUrl = 'http://localhost:8080';
  static const String wsUrl = 'ws://localhost:8081';
  
  // API Endpoints
  static const String apps = '/api/apps';
  static const String timeline = '/api/timeline';
  static const String tracking = '/api/tracking';
  static const String statistics = '/api/statistics';
  
  // WebSocket Events
  static const String trackingStatusUpdate = 'tracking_status_update';
  static const String appUpdate = 'app_update';
  static const String sessionUpdate = 'session_update';
  
  // Commands
  static const String startTracking = 'start_tracking';
  static const String stopTracking = 'stop_tracking';
  static const String pauseTracking = 'pause_tracking';
  static const String resumeTracking = 'resume_tracking';
}

class AppConstants {
  static const String appName = 'Time Tracker';
  static const String version = '1.0.0';
  
  // Storage Keys
  static const String themeKey = 'theme_mode';
  static const String serverUrlKey = 'server_url';
  static const String autoStartKey = 'auto_start_tracking';
  
  // Default Values
  static const int defaultRefreshInterval = 5; // seconds
  static const int maxRecentApps = 10;
  static const int chartDataPoints = 30; // days
}

class UIConstants {
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 1024;
  
  // Spacing
  static const double spacingXS = 4.0;
  static const double spacingS = 8.0;
  static const double spacingM = 16.0;
  static const double spacingL = 24.0;
  static const double spacingXL = 32.0;
  
  // Border Radius
  static const double radiusS = 4.0;
  static const double radiusM = 8.0;
  static const double radiusL = 12.0;
  static const double radiusXL = 16.0;
  
  // Animation Durations
  static const Duration animationFast = Duration(milliseconds: 200);
  static const Duration animationNormal = Duration(milliseconds: 300);
  static const Duration animationSlow = Duration(milliseconds: 500);
}

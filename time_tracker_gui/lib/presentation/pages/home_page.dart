import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../widgets/dashboard_card.dart';
import '../widgets/tracking_controls.dart';
import '../widgets/app_list_widget.dart';
import '../widgets/statistics_chart.dart';
import '../providers/app_providers.dart';
import '../providers/theme_provider.dart';
import '../../core/constants/ui_constants.dart';

class HomePage extends ConsumerStatefulWidget {
  const HomePage({super.key});

  @override
  ConsumerState<HomePage> createState() => _HomePageState();
}

class _HomePageState extends ConsumerState<HomePage> with TickerProviderStateMixin {
  late TabController _tabController;
  int _selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);

    // Initialize connection to backend
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(timeTrackerRepositoryProvider).connect();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    ref.read(timeTrackerRepositoryProvider).disconnect();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Time Tracker'),
        actions: [
          IconButton(
            icon: const Icon(Icons.brightness_6),
            onPressed: () => ref.read(themeModeProvider.notifier).toggleTheme(),
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => _showSettingsDialog(context),
          ),
        ],
        bottom: _isDesktop(context) ? null : TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'Dashboard'),
            Tab(icon: Icon(Icons.apps), text: 'Apps'),
            Tab(icon: Icon(Icons.timeline), text: 'Timeline'),
            Tab(icon: Icon(Icons.analytics), text: 'Statistics'),
          ],
        ),
      ),
      body: _isDesktop(context) ? _buildDesktopLayout() : _buildMobileLayout(),
      bottomNavigationBar: _isDesktop(context) ? null : _buildBottomNavigation(),
    );
  }

  Widget _buildDesktopLayout() {
    return Row(
      children: [
        NavigationRail(
          selectedIndex: _selectedIndex,
          onDestinationSelected: (index) => setState(() => _selectedIndex = index),
          labelType: NavigationRailLabelType.all,
          destinations: const [
            NavigationRailDestination(
              icon: Icon(Icons.dashboard),
              label: Text('Dashboard'),
            ),
            NavigationRailDestination(
              icon: Icon(Icons.apps),
              label: Text('Apps'),
            ),
            NavigationRailDestination(
              icon: Icon(Icons.timeline),
              label: Text('Timeline'),
            ),
            NavigationRailDestination(
              icon: Icon(Icons.analytics),
              label: Text('Statistics'),
            ),
          ],
        ),
        const VerticalDivider(thickness: 1, width: 1),
        Expanded(child: _buildPageContent(_selectedIndex)),
      ],
    );
  }

  Widget _buildMobileLayout() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildPageContent(0),
        _buildPageContent(1),
        _buildPageContent(2),
        _buildPageContent(3),
      ],
    );
  }

  Widget _buildPageContent(int index) {
    switch (index) {
      case 0:
        return _buildDashboard();
      case 1:
        return _buildAppsPage();
      case 2:
        return _buildTimelinePage();
      case 3:
        return _buildStatisticsPage();
      default:
        return _buildDashboard();
    }
  }

  Widget _buildDashboard() {
    final trackingStatus = ref.watch(trackingStatusProvider);
    final apps = ref.watch(appsProvider);

    return SingleChildScrollView(
      padding: EdgeInsets.all(UIConstants.spacingM.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Tracking Status Card
          DashboardCard(
            title: 'Tracking Status',
            child: trackingStatus.when(
              data: (status) => TrackingControls(status: status),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, _) => Text('Error: $error'),
            ),
          ),
          SizedBox(height: UIConstants.spacingM.h),

          // Quick Stats
          Row(
            children: [
              Expanded(
                child: DashboardCard(
                  title: 'Total Apps',
                  child: apps.when(
                    data: (appList) => Text(
                      '${appList.length}',
                      style: Theme.of(context).textTheme.headlineMedium,
                    ),
                    loading: () => const CircularProgressIndicator(),
                    error: (_, __) => const Text('Error'),
                  ),
                ),
              ),
              SizedBox(width: UIConstants.spacingM.w),
              Expanded(
                child: DashboardCard(
                  title: 'Today\'s Time',
                  child: trackingStatus.when(
                    data: (status) => Text(
                      _formatDuration(status.currentSessionDuration),
                      style: Theme.of(context).textTheme.headlineMedium,
                    ),
                    loading: () => const CircularProgressIndicator(),
                    error: (_, __) => const Text('Error'),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: UIConstants.spacingM.h),

          // Recent Apps
          DashboardCard(
            title: 'Recent Apps',
            child: apps.when(
              data: (appList) => AppListWidget(
                apps: appList.take(5).toList(),
                isCompact: true,
              ),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, _) => Text('Error: $error'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppsPage() {
    final apps = ref.watch(appsProvider);

    return Column(
      children: [
        Padding(
          padding: EdgeInsets.all(UIConstants.spacingM.w),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  decoration: const InputDecoration(
                    hintText: 'Search apps...',
                    prefixIcon: Icon(Icons.search),
                  ),
                  onChanged: (value) {
                    // TODO: Implement search functionality
                  },
                ),
              ),
              SizedBox(width: UIConstants.spacingM.w),
              ElevatedButton.icon(
                onPressed: () => _showAddAppDialog(context),
                icon: const Icon(Icons.add),
                label: const Text('Add App'),
              ),
            ],
          ),
        ),
        Expanded(
          child: apps.when(
            data: (appList) => AppListWidget(apps: appList),
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, _) => Center(child: Text('Error: $error')),
          ),
        ),
      ],
    );
  }

  Widget _buildTimelinePage() {
    return const Center(
      child: Text('Timeline View - Coming Soon'),
    );
  }

  Widget _buildStatisticsPage() {
    final statistics = ref.watch(statisticsProvider);

    return SingleChildScrollView(
      padding: EdgeInsets.all(UIConstants.spacingM.w),
      child: Column(
        children: [
          DashboardCard(
            title: 'Usage Statistics',
            child: statistics.when(
              data: (stats) => StatisticsChart(statistics: stats),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, _) => Text('Error: $error'),
            ),
          ),
        ],
      ),
    );
  }

  Widget? _buildBottomNavigation() {
    if (_isDesktop(context)) return null;

    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      currentIndex: _tabController.index,
      onTap: (index) => _tabController.animateTo(index),
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.dashboard),
          label: 'Dashboard',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.apps),
          label: 'Apps',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.timeline),
          label: 'Timeline',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.analytics),
          label: 'Statistics',
        ),
      ],
    );
  }

  bool _isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width > UIConstants.tabletBreakpoint;
  }

  String _formatDuration(int seconds) {
    final hours = seconds ~/ 3600;
    final minutes = (seconds % 3600) ~/ 60;
    final secs = seconds % 60;

    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else if (minutes > 0) {
      return '${minutes}m ${secs}s';
    } else {
      return '${secs}s';
    }
  }

  void _showSettingsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Settings'),
        content: const Text('Settings dialog - Coming Soon'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showAddAppDialog(BuildContext context) {
    final controller = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add New App'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            hintText: 'Enter app name...',
            labelText: 'App Name',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (controller.text.isNotEmpty) {
                ref.read(appsProvider.notifier).addApp(controller.text);
                Navigator.of(context).pop();
              }
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }
}

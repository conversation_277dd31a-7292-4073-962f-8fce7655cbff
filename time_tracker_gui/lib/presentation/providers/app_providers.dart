import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/models/app_model.dart';
import '../../data/repositories/time_tracker_repository.dart';
import '../../data/services/api_service.dart';
import '../../data/services/websocket_service.dart';

// Service Providers
final apiServiceProvider = Provider<ApiService>((ref) => ApiService());

final webSocketServiceProvider = Provider<WebSocketService>((ref) => WebSocketService());

final timeTrackerRepositoryProvider = Provider<TimeTrackerRepository>((ref) {
  return TimeTrackerRepositoryImpl(
    apiService: ref.read(apiServiceProvider),
    webSocketService: ref.read(webSocketServiceProvider),
  );
});

// State Providers
final appsProvider = StateNotifierProvider<AppsNotifier, AsyncValue<List<AppModel>>>((ref) {
  return AppsNotifier(ref.read(timeTrackerRepositoryProvider));
});

final trackingStatusProvider = StateNotifierProvider<TrackingStatusNotifier, AsyncValue<TrackingStatus>>((ref) {
  return TrackingStatusNotifier(ref.read(timeTrackerRepositoryProvider));
});

final statisticsProvider = StateNotifierProvider<StatisticsNotifier, AsyncValue<List<AppStatistics>>>((ref) {
  return StatisticsNotifier(ref.read(timeTrackerRepositoryProvider));
});

final timelineProvider = StateNotifierProvider<TimelineNotifier, AsyncValue<List<TimelineModel>>>((ref) {
  return TimelineNotifier(ref.read(timeTrackerRepositoryProvider));
});

// State Notifiers
class AppsNotifier extends StateNotifier<AsyncValue<List<AppModel>>> {
  final TimeTrackerRepository _repository;

  AppsNotifier(this._repository) : super(const AsyncValue.loading()) {
    loadApps();
    _listenToAppUpdates();
  }

  Future<void> loadApps() async {
    try {
      state = const AsyncValue.loading();
      final apps = await _repository.getAllApps();
      state = AsyncValue.data(apps);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> addApp(String name) async {
    try {
      await _repository.insertApp(name);
      await loadApps(); // Refresh the list
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deleteApp(int appId) async {
    try {
      await _repository.deleteApp(appId);
      await loadApps(); // Refresh the list
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  void _listenToAppUpdates() {
    _repository.appUpdateStream.listen((updatedApp) {
      state.whenData((apps) {
        final updatedApps = apps.map((app) {
          return app.id == updatedApp.id ? updatedApp : app;
        }).toList();
        state = AsyncValue.data(updatedApps);
      });
    });
  }
}

class TrackingStatusNotifier extends StateNotifier<AsyncValue<TrackingStatus>> {
  final TimeTrackerRepository _repository;

  TrackingStatusNotifier(this._repository) : super(const AsyncValue.loading()) {
    loadTrackingStatus();
    _listenToStatusUpdates();
  }

  Future<void> loadTrackingStatus() async {
    try {
      state = const AsyncValue.loading();
      final status = await _repository.getTrackingStatus();
      state = AsyncValue.data(status);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> startTracking() async {
    try {
      await _repository.startTracking();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> stopTracking() async {
    try {
      await _repository.stopTracking();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> pauseTracking() async {
    try {
      await _repository.pauseTracking();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> resumeTracking() async {
    try {
      await _repository.resumeTracking();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  void _listenToStatusUpdates() {
    _repository.trackingStatusStream.listen((status) {
      state = AsyncValue.data(status);
    });
  }
}

class StatisticsNotifier extends StateNotifier<AsyncValue<List<AppStatistics>>> {
  final TimeTrackerRepository _repository;

  StatisticsNotifier(this._repository) : super(const AsyncValue.loading()) {
    loadStatistics();
  }

  Future<void> loadStatistics({DateTime? startDate, DateTime? endDate}) async {
    try {
      state = const AsyncValue.loading();
      final statistics = await _repository.getStatistics(
        startDate: startDate,
        endDate: endDate,
      );
      state = AsyncValue.data(statistics);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

class TimelineNotifier extends StateNotifier<AsyncValue<List<TimelineModel>>> {
  final TimeTrackerRepository _repository;

  TimelineNotifier(this._repository) : super(const AsyncValue.loading()) {
    loadTimeline();
  }

  Future<void> loadTimeline({
    DateTime? startDate,
    DateTime? endDate,
    int? appId,
  }) async {
    try {
      state = const AsyncValue.loading();
      final timeline = await _repository.getTimeline(
        startDate: startDate,
        endDate: endDate,
        appId: appId,
      );
      state = AsyncValue.data(timeline);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../data/models/app_model.dart';
import '../providers/app_providers.dart';
import '../../core/constants/ui_constants.dart';
import '../../app/theme/app_theme.dart';

class TrackingControls extends ConsumerWidget {
  final TrackingStatus status;

  const TrackingControls({
    super.key,
    required this.status,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        _buildStatusIndicator(context),
        SizedBox(height: UIConstants.spacingM.h),
        _buildCurrentApp(context),
        SizedBox(height: UIConstants.spacingM.h),
        _buildSessionInfo(context),
        SizedBox(height: UIConstants.spacingL.h),
        _buildControlButtons(context, ref),
      ],
    );
  }

  Widget _buildStatusIndicator(BuildContext context) {
    Color statusColor;
    String statusText;
    IconData statusIcon;

    if (status.isTracking && !status.isPaused) {
      statusColor = Theme.of(context).successColor;
      statusText = 'Tracking Active';
      statusIcon = Icons.play_circle_filled;
    } else if (status.isPaused) {
      statusColor = Theme.of(context).warningColor;
      statusText = 'Tracking Paused';
      statusIcon = Icons.pause_circle_filled;
    } else {
      statusColor = Theme.of(context).colorScheme.error;
      statusText = 'Tracking Stopped';
      statusIcon = Icons.stop_circle;
    }

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UIConstants.spacingM.w,
        vertical: UIConstants.spacingS.h,
      ),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(UIConstants.radiusL),
        border: Border.all(color: statusColor.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(statusIcon, color: statusColor, size: 20.sp),
          SizedBox(width: UIConstants.spacingS.w),
          Text(
            statusText,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: statusColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentApp(BuildContext context) {
    if (status.currentApp == null) {
      return Text(
        'No app being tracked',
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
      );
    }

    return Column(
      children: [
        Text(
          'Currently Tracking',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        SizedBox(height: UIConstants.spacingXS.h),
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: UIConstants.spacingM.w,
            vertical: UIConstants.spacingS.h,
          ),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primaryContainer,
            borderRadius: BorderRadius.circular(UIConstants.radiusM),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.apps,
                color: Theme.of(context).colorScheme.onPrimaryContainer,
                size: 18.sp,
              ),
              SizedBox(width: UIConstants.spacingS.w),
              Text(
                status.currentApp!,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSessionInfo(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildInfoItem(
          context,
          'Session Time',
          _formatDuration(status.currentSessionDuration),
          Icons.timer,
        ),
        _buildInfoItem(
          context,
          'Started',
          status.sessionStartTime != null
              ? _formatTime(status.sessionStartTime!)
              : 'N/A',
          Icons.schedule,
        ),
      ],
    );
  }

  Widget _buildInfoItem(BuildContext context, String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: Theme.of(context).colorScheme.primary,
          size: 20.sp,
        ),
        SizedBox(height: UIConstants.spacingXS.h),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildControlButtons(BuildContext context, WidgetRef ref) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        if (!status.isTracking)
          ElevatedButton.icon(
            onPressed: () => ref.read(trackingStatusProvider.notifier).startTracking(),
            icon: const Icon(Icons.play_arrow),
            label: const Text('Start'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).successColor,
              foregroundColor: Colors.white,
            ),
          ),
        
        if (status.isTracking && !status.isPaused)
          ElevatedButton.icon(
            onPressed: () => ref.read(trackingStatusProvider.notifier).pauseTracking(),
            icon: const Icon(Icons.pause),
            label: const Text('Pause'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).warningColor,
              foregroundColor: Colors.white,
            ),
          ),
        
        if (status.isTracking && status.isPaused)
          ElevatedButton.icon(
            onPressed: () => ref.read(trackingStatusProvider.notifier).resumeTracking(),
            icon: const Icon(Icons.play_arrow),
            label: const Text('Resume'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).successColor,
              foregroundColor: Colors.white,
            ),
          ),
        
        if (status.isTracking)
          ElevatedButton.icon(
            onPressed: () => ref.read(trackingStatusProvider.notifier).stopTracking(),
            icon: const Icon(Icons.stop),
            label: const Text('Stop'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Colors.white,
            ),
          ),
      ],
    );
  }

  String _formatDuration(int seconds) {
    final hours = seconds ~/ 3600;
    final minutes = (seconds % 3600) ~/ 60;
    final secs = seconds % 60;
    
    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
    }
  }

  String _formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../data/models/app_model.dart';
import '../providers/app_providers.dart';
import '../../core/constants/ui_constants.dart';

class AppListWidget extends ConsumerWidget {
  final List<AppModel> apps;
  final bool isCompact;

  const AppListWidget({
    super.key,
    required this.apps,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (apps.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.apps,
              size: 64.sp,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            SizedBox(height: UIConstants.spacingM.h),
            Text(
              'No apps tracked yet',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            SizedBox(height: UIConstants.spacingS.h),
            Text(
              'Add an app to start tracking',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: isCompact,
      physics: isCompact ? const NeverScrollableScrollPhysics() : null,
      itemCount: apps.length,
      itemBuilder: (context, index) {
        final app = apps[index];
        return AppListItem(
          app: app,
          isCompact: isCompact,
          onDelete: () => _showDeleteConfirmation(context, ref, app),
        );
      },
    );
  }

  void _showDeleteConfirmation(BuildContext context, WidgetRef ref, AppModel app) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete App'),
        content: Text('Are you sure you want to delete "${app.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(appsProvider.notifier).deleteApp(app.id);
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}

class AppListItem extends StatelessWidget {
  final AppModel app;
  final bool isCompact;
  final VoidCallback? onDelete;

  const AppListItem({
    super.key,
    required this.app,
    this.isCompact = false,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.symmetric(
        horizontal: isCompact ? 0 : UIConstants.spacingS.w,
        vertical: UIConstants.spacingXS.h,
      ),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Theme.of(context).colorScheme.primaryContainer,
          child: Icon(
            Icons.apps,
            color: Theme.of(context).colorScheme.onPrimaryContainer,
          ),
        ),
        title: Text(
          app.name ?? 'Unknown App',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: isCompact ? null : Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (app.productName != null)
              Text(
                app.productName!,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            SizedBox(height: UIConstants.spacingXS.h),
            Row(
              children: [
                _buildStatChip(
                  context,
                  'Duration',
                  _formatDuration(app.duration ?? 0),
                  Icons.timer,
                ),
                SizedBox(width: UIConstants.spacingS.w),
                _buildStatChip(
                  context,
                  'Launches',
                  '${app.launches ?? 0}',
                  Icons.launch,
                ),
              ],
            ),
          ],
        ),
        trailing: isCompact ? null : Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _formatDuration(app.duration ?? 0),
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            if (onDelete != null) ...[
              SizedBox(width: UIConstants.spacingS.w),
              IconButton(
                icon: const Icon(Icons.delete),
                onPressed: onDelete,
                color: Theme.of(context).colorScheme.error,
              ),
            ],
          ],
        ),
        onTap: () => _showAppDetails(context),
      ),
    );
  }

  Widget _buildStatChip(BuildContext context, String label, String value, IconData icon) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UIConstants.spacingS.w,
        vertical: UIConstants.spacingXS.h,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant,
        borderRadius: BorderRadius.circular(UIConstants.radiusS),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 12.sp,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          SizedBox(width: UIConstants.spacingXS.w),
          Text(
            value,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDuration(int minutes) {
    if (minutes < 60) {
      return '${minutes}m';
    } else {
      final hours = minutes ~/ 60;
      final remainingMinutes = minutes % 60;
      return '${hours}h ${remainingMinutes}m';
    }
  }

  void _showAppDetails(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(app.name ?? 'Unknown App'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('Product Name', app.productName ?? 'N/A'),
            _buildDetailRow('Total Duration', _formatDuration(app.duration ?? 0)),
            _buildDetailRow('Launches', '${app.launches ?? 0}'),
            _buildDetailRow('Longest Session', _formatDuration(app.longestSession ?? 0)),
            _buildDetailRow('Longest Session Date', 
              app.longestSessionOn?.toString().split(' ')[0] ?? 'N/A'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: UIConstants.spacingXS.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120.w,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }
}

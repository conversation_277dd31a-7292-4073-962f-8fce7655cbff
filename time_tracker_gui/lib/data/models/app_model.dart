import 'package:freezed_annotation/freezed_annotation.dart';

part 'app_model.freezed.dart';
part 'app_model.g.dart';

@freezed
class AppModel with _$AppModel {
  const factory AppModel({
    required int id,
    String? name,
    String? productName,
    int? duration,
    int? launches,
    int? longestSession,
    DateTime? longestSessionOn,
  }) = _AppModel;

  factory AppModel.fromJson(Map<String, dynamic> json) => _$AppModelFromJson(json);
}

@freezed
class TimelineModel with _$TimelineModel {
  const factory TimelineModel({
    required int id,
    DateTime? date,
    int? duration,
    required int appId,
  }) = _TimelineModel;

  factory TimelineModel.fromJson(Map<String, dynamic> json) => _$TimelineModelFromJson(json);
}

@freezed
class TrackingStatus with _$TrackingStatus {
  const factory TrackingStatus({
    @Default(false) bool isTracking,
    @Default(false) bool isPaused,
    String? currentApp,
    @Default(0) int currentSessionDuration,
    DateTime? sessionStartTime,
  }) = _TrackingStatus;

  factory TrackingStatus.fromJson(Map<String, dynamic> json) => _$TrackingStatusFromJson(json);
}

@freezed
class AppStatistics with _$AppStatistics {
  const factory AppStatistics({
    required AppModel app,
    @Default(0) int totalDuration,
    @Default(0) int todayDuration,
    @Default(0) int weekDuration,
    @Default(0) int monthDuration,
    @Default(0) double averageSessionLength,
    @Default([]) List<TimelineModel> recentSessions,
  }) = _AppStatistics;

  factory AppStatistics.fromJson(Map<String, dynamic> json) => _$AppStatisticsFromJson(json);
}

import 'dart:async';
import 'dart:convert';
import 'package:web_socket_channel/web_socket_channel.dart';
import '../models/app_model.dart';
import '../../core/constants/api_constants.dart';

class WebSocketService {
  WebSocketChannel? _channel;
  final String _wsUrl;
  
  final StreamController<TrackingStatus> _trackingStatusController = StreamController.broadcast();
  final StreamController<AppModel> _appUpdateController = StreamController.broadcast();
  final StreamController<Map<String, dynamic>> _sessionUpdateController = StreamController.broadcast();
  
  Stream<TrackingStatus> get trackingStatusStream => _trackingStatusController.stream;
  Stream<AppModel> get appUpdateStream => _appUpdateController.stream;
  Stream<Map<String, dynamic>> get sessionUpdateStream => _sessionUpdateController.stream;
  
  bool _isConnected = false;
  Timer? _reconnectTimer;
  int _reconnectAttempts = 0;
  static const int maxReconnectAttempts = 5;
  static const Duration reconnectDelay = Duration(seconds: 5);

  WebSocketService({String? wsUrl}) : _wsUrl = wsUrl ?? ApiConstants.wsUrl;

  bool get isConnected => _isConnected;

  Future<void> connect() async {
    try {
      _channel = WebSocketChannel.connect(Uri.parse(_wsUrl));
      _isConnected = true;
      _reconnectAttempts = 0;
      
      _channel!.stream.listen(
        _handleMessage,
        onError: _handleError,
        onDone: _handleDisconnection,
      );
      
      print('WebSocket connected to $_wsUrl');
    } catch (e) {
      print('WebSocket connection failed: $e');
      _scheduleReconnect();
    }
  }

  void _handleMessage(dynamic message) {
    try {
      final data = json.decode(message as String);
      final eventType = data['type'] as String?;
      final payload = data['payload'] as Map<String, dynamic>?;

      if (payload == null) return;

      switch (eventType) {
        case ApiConstants.trackingStatusUpdate:
          final status = TrackingStatus.fromJson(payload);
          _trackingStatusController.add(status);
          break;
          
        case ApiConstants.appUpdate:
          final app = AppModel.fromJson(payload);
          _appUpdateController.add(app);
          break;
          
        case ApiConstants.sessionUpdate:
          _sessionUpdateController.add(payload);
          break;
          
        default:
          print('Unknown WebSocket event type: $eventType');
      }
    } catch (e) {
      print('Error parsing WebSocket message: $e');
    }
  }

  void _handleError(error) {
    print('WebSocket error: $error');
    _isConnected = false;
    _scheduleReconnect();
  }

  void _handleDisconnection() {
    print('WebSocket disconnected');
    _isConnected = false;
    _scheduleReconnect();
  }

  void _scheduleReconnect() {
    if (_reconnectAttempts >= maxReconnectAttempts) {
      print('Max reconnection attempts reached');
      return;
    }

    _reconnectTimer?.cancel();
    _reconnectTimer = Timer(reconnectDelay, () {
      _reconnectAttempts++;
      print('Attempting to reconnect... (attempt $_reconnectAttempts)');
      connect();
    });
  }

  void sendMessage(String type, Map<String, dynamic> payload) {
    if (!_isConnected || _channel == null) {
      print('WebSocket not connected, cannot send message');
      return;
    }

    final message = json.encode({
      'type': type,
      'payload': payload,
    });

    try {
      _channel!.sink.add(message);
    } catch (e) {
      print('Error sending WebSocket message: $e');
    }
  }

  void sendTrackingCommand(String command) {
    sendMessage('command', {'action': command});
  }

  void disconnect() {
    _reconnectTimer?.cancel();
    _channel?.sink.close();
    _isConnected = false;
  }

  void dispose() {
    disconnect();
    _trackingStatusController.close();
    _appUpdateController.close();
    _sessionUpdateController.close();
  }
}

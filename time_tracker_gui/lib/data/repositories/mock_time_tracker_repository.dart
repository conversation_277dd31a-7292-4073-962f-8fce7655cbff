import '../models/app_model.dart';
import '../services/mock_backend_service.dart';
import 'time_tracker_repository.dart';

class MockTimeTrackerRepository implements TimeTrackerRepository {
  final MockBackendService _mockService = MockBackendService();

  @override
  Future<List<AppModel>> getAllApps() => _mockService.getAllApps();

  @override
  Future<AppModel?> getAppByName(String name) => _mockService.getAppByName(name);

  @override
  Future<void> insertApp(String name) => _mockService.insertApp(name);

  @override
  Future<void> deleteApp(int appId) => _mockService.deleteApp(appId);

  @override
  Future<List<TimelineModel>> getTimeline({
    DateTime? startDate,
    DateTime? endDate,
    int? appId,
  }) => _mockService.getTimeline(
    startDate: startDate,
    endDate: endDate,
    appId: appId,
  );

  @override
  Future<TrackingStatus> getTrackingStatus() => _mockService.getTrackingStatus();

  @override
  Future<void> startTracking() => _mockService.startTracking();

  @override
  Future<void> stopTracking() => _mockService.stopTracking();

  @override
  Future<void> pauseTracking() => _mockService.pauseTracking();

  @override
  Future<void> resumeTracking() => _mockService.resumeTracking();

  @override
  Future<List<AppStatistics>> getStatistics({
    DateTime? startDate,
    DateTime? endDate,
  }) => _mockService.getStatistics();

  @override
  Stream<TrackingStatus> get trackingStatusStream => _mockService.trackingStatusStream;

  @override
  Stream<AppModel> get appUpdateStream => _mockService.appUpdateStream;

  @override
  Stream<Map<String, dynamic>> get sessionUpdateStream => const Stream.empty();

  @override
  Future<void> connect() async {
    // Start the mock session for demonstration
    _mockService.startMockSession();
  }

  @override
  void disconnect() {
    _mockService.dispose();
  }
}

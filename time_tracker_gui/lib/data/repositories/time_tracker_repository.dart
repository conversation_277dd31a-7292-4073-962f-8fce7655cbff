import '../models/app_model.dart';
import '../services/api_service.dart';
import '../services/websocket_service.dart';

abstract class TimeTrackerRepository {
  Future<List<AppModel>> getAllApps();
  Future<AppModel?> getAppByName(String name);
  Future<void> insertApp(String name);
  Future<void> deleteApp(int appId);
  Future<List<TimelineModel>> getTimeline({DateTime? startDate, DateTime? endDate, int? appId});
  Future<TrackingStatus> getTrackingStatus();
  Future<void> startTracking();
  Future<void> stopTracking();
  Future<void> pauseTracking();
  Future<void> resumeTracking();
  Future<List<AppStatistics>> getStatistics({DateTime? startDate, DateTime? endDate});
  
  Stream<TrackingStatus> get trackingStatusStream;
  Stream<AppModel> get appUpdateStream;
  Stream<Map<String, dynamic>> get sessionUpdateStream;
  
  Future<void> connect();
  void disconnect();
}

class TimeTrackerRepositoryImpl implements TimeTrackerRepository {
  final ApiService _apiService;
  final WebSocketService _webSocketService;

  TimeTrackerRepositoryImpl({
    required ApiService apiService,
    required WebSocketService webSocketService,
  }) : _apiService = apiService,
       _webSocketService = webSocketService;

  @override
  Future<List<AppModel>> getAllApps() => _apiService.getAllApps();

  @override
  Future<AppModel?> getAppByName(String name) => _apiService.getAppByName(name);

  @override
  Future<void> insertApp(String name) => _apiService.insertApp(name);

  @override
  Future<void> deleteApp(int appId) => _apiService.deleteApp(appId);

  @override
  Future<List<TimelineModel>> getTimeline({
    DateTime? startDate,
    DateTime? endDate,
    int? appId,
  }) => _apiService.getTimeline(
    startDate: startDate,
    endDate: endDate,
    appId: appId,
  );

  @override
  Future<TrackingStatus> getTrackingStatus() => _apiService.getTrackingStatus();

  @override
  Future<void> startTracking() => _apiService.sendTrackingCommand('start');

  @override
  Future<void> stopTracking() => _apiService.sendTrackingCommand('stop');

  @override
  Future<void> pauseTracking() => _apiService.sendTrackingCommand('pause');

  @override
  Future<void> resumeTracking() => _apiService.sendTrackingCommand('resume');

  @override
  Future<List<AppStatistics>> getStatistics({
    DateTime? startDate,
    DateTime? endDate,
  }) => _apiService.getStatistics(
    startDate: startDate,
    endDate: endDate,
  );

  @override
  Stream<TrackingStatus> get trackingStatusStream => _webSocketService.trackingStatusStream;

  @override
  Stream<AppModel> get appUpdateStream => _webSocketService.appUpdateStream;

  @override
  Stream<Map<String, dynamic>> get sessionUpdateStream => _webSocketService.sessionUpdateStream;

  @override
  Future<void> connect() async {
    await _webSocketService.connect();
  }

  @override
  void disconnect() {
    _webSocketService.disconnect();
  }

  void dispose() {
    _apiService.dispose();
    _webSocketService.dispose();
  }
}

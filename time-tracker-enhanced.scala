//> using dep org.tpolecat::skunk-core::0.6.4
//> using dep org.slf4j:slf4j-nop:2.0.17
//> using dep com.github.alexarchambault::case-app::2.1.0-M30
//> using dep com.lihaoyi::os-lib::0.11.4

import cats.effect._
import cats.syntax.all._
import fs2._
import fs2.concurrent.SignallingRef
import skunk._
import skunk.codec.all._
import skunk.implicits._
import natchez.Trace.Implicits.noop

import caseapp._
import scala.concurrent.duration._
import java.time.LocalDate
import scala.util.Try

// Data Models
case class App(
    id: Int,
    name: Option[String],
    productName: Option[String],
    duration: Option[Int],
    launches: Option[Int],
    longestSession: Option[Int],
    longestSessionOn: Option[LocalDate]
)

case class Timeline(
    id: Int,
    date: Option[LocalDate],
    duration: Option[Int],
    appId: Int
)

// Configuration
@AppName("time_tracker_enhanced")
@AppVersion("2.0.0")
case class Config(
    @HelpMessage("Display data for process and return")
    display: Option[String] = None,
    @HelpMessage("Display data for all processes")
    displayAll: Boolean = false,
    @HelpMessage("Insert process if it is not already tracked")
    insert: Option[String] = None,
    @HelpMessage("Print debug output")
    debug: Boolean = false,
    @HelpMessage("Pause/Unpause tracking")
    pause: Boolean = false
)

// Database Operations
class DatabaseOps(session: Session[IO]) {
  import skunk.{Query => SkunkQuery, Command => SkunkCommand, Void}

  // Codecs
  private val appCodec: Codec[App] =
    (int4 ~ varchar(255).opt ~ varchar(255).opt ~ int4.opt ~ int4.opt ~ int4.opt ~ date.opt)
      .imap {
        case id ~ name ~ productName ~ duration ~ launches ~ longestSession ~ longestSessionOn =>
          App(id, name, productName, duration, launches, longestSession, longestSessionOn)
      }(app => app.id ~ app.name ~ app.productName ~ app.duration ~ app.launches ~ app.longestSession ~ app.longestSessionOn)

  // Queries
  private val selectAppByName: SkunkQuery[String, App] =
    sql"SELECT id, name, product_name, duration, launches, longest_session, longest_session_on FROM apps WHERE name = $varchar"
      .query(appCodec)

  private val selectAllApps: SkunkQuery[Void, App] =
    sql"SELECT id, name, product_name, duration, launches, longest_session, longest_session_on FROM apps ORDER BY id"
      .query(appCodec)

  private val selectAllAppNames: SkunkQuery[Void, String] =
    sql"SELECT name FROM apps WHERE name IS NOT NULL"
      .query(varchar(255))

  // Commands
  private val insertApp: SkunkCommand[String] =
    sql"INSERT INTO apps (name) VALUES ($varchar)"
      .command

  private val updateAppDuration: SkunkCommand[(Int, Int)] =
    sql"UPDATE apps SET duration = $int4 WHERE id = $int4"
      .command

  private val updateAppLaunches: SkunkCommand[(Int, Int)] =
    sql"UPDATE apps SET launches = $int4 WHERE id = $int4"
      .command

  private val updateLongestSession: SkunkCommand[(Int, LocalDate, Int)] =
    sql"UPDATE apps SET longest_session = $int4, longest_session_on = $date WHERE id = $int4"
      .command

  private val upsertTimeline: SkunkCommand[(String, LocalDate)] =
    sql"CALL upsert_timeline($varchar, $date)"
      .command

  // Operations
  def getAppByName(name: String): IO[Option[App]] =
    session.prepare(selectAppByName).flatMap(_.option(name))

  def getAllApps: IO[List[App]] =
    session.prepare(selectAllApps).flatMap(_.stream(Void, 1024).compile.toList)

  def getAllAppNames: IO[List[String]] =
    session.prepare(selectAllAppNames).flatMap(_.stream(Void, 1024).compile.toList)

  def insertNewApp(name: String): IO[Unit] =
    session.prepare(insertApp).flatMap(_.execute(name)).void

  def updateDuration(appId: Int, duration: Int): IO[Unit] =
    session.prepare(updateAppDuration).flatMap(_.execute((duration, appId))).void

  def updateLaunches(appId: Int, launches: Int): IO[Unit] =
    session.prepare(updateAppLaunches).flatMap(_.execute((launches, appId))).void

  def updateLongestSessionRecord(appId: Int, sessionDuration: Int): IO[Unit] =
    session.prepare(updateLongestSession).flatMap(_.execute((sessionDuration, LocalDate.now(), appId))).void

  def updateTimeline(processName: String): IO[Unit] =
    session.prepare(upsertTimeline).flatMap(_.execute((processName, LocalDate.now()))).void
}

// Process Utilities
object ProcessUtils {
  def isProcessRunning(processName: String): IO[Boolean] =
    IO {
      Try {
        os.proc("pgrep", "-f", processName).call(check = false).exitCode == 0
      }.getOrElse(false)
    }

  def sendNotification(message: String): IO[Unit] =
    IO {
      Try {
        os.proc("notify-send", "time_tracker", message).call(check = false)
      }.recover { case _ => () }
    }.void
}

// Process Monitor
class ProcessMonitor(db: DatabaseOps, pauseSignal: SignallingRef[IO, Boolean], debug: Boolean) {

  case class MonitorState(
    currentDuration: Int = 0,
    totalDuration: Int = 0,
    launched: Boolean = false
  )

  def monitorProcess(processName: String, app: App): IO[Unit] = {
    val initialState = MonitorState(totalDuration = app.duration.getOrElse(0))

    ProcessUtils.sendNotification(s"Started tracking \"$processName\"") *>
    Stream.awakeEvery[IO](1.minute)
      .evalMap { _ =>
        pauseSignal.get.flatMap { isPaused =>
          if (isPaused) {
            if (debug) IO.println(s"Monitoring paused for $processName") else IO.unit *>
            IO.pure(None)
          } else {
            ProcessUtils.isProcessRunning(processName).map(Some(_))
          }
        }
      }
      .collect { case Some(isRunning) => isRunning }
      .takeWhile(identity) // Stop the stream when process is no longer running
      .evalMapAccumulate(initialState) { (state, _) =>
        val newState = state.copy(
          currentDuration = state.currentDuration + 1,
          totalDuration = state.totalDuration + 1
        )

        val updateLaunches = if (!state.launched) {
          val newLaunches = app.launches.getOrElse(0) + 1
          db.updateLaunches(app.id, newLaunches) *>
          (if (debug) IO.println(s"Updated launches for $processName to $newLaunches") else IO.unit) *>
          IO.pure(newState.copy(launched = true))
        } else IO.pure(newState)

        updateLaunches.flatMap { updatedState =>
          // Update database every second
          db.updateDuration(app.id, updatedState.totalDuration) *>
          db.updateTimeline(processName) *>
          (if (debug) IO.println(s"[$processName] duration: ${updatedState.totalDuration}, session: ${updatedState.currentDuration}") else IO.unit) *>
          IO.pure((updatedState, ()))
        }
      }
      .interruptWhen(pauseSignal.map(identity))
      .compile
      .lastOrError
      .flatMap { case (finalState, _) =>
        // Handle termination - this runs only once when the stream ends
        for {
          _ <- if (debug) IO.println(s"Process $processName stopped. Session: ${finalState.currentDuration}, Total: ${finalState.totalDuration}") else IO.unit

          // Final database update
          _ <- db.updateDuration(app.id, finalState.totalDuration)

          // Update longest session if needed
          _ <- if (finalState.currentDuration > app.longestSession.getOrElse(0)) {
            db.updateLongestSessionRecord(app.id, finalState.currentDuration) *>
            (if (debug) IO.println(s"New longest session for $processName: ${finalState.currentDuration} seconds") else IO.unit)
          } else IO.unit

          // Send notification
          _ <- ProcessUtils.sendNotification(s"process: \"$processName\" | session: ${finalState.currentDuration} | total: ${finalState.totalDuration}")
        } yield ()
      }
  }
}

// Discovery Service
class DiscoveryService(
    db: DatabaseOps,
    processMonitor: ProcessMonitor,
    pauseSignal: SignallingRef[IO, Boolean],
    debug: Boolean
) {

  private val runningProcesses = scala.collection.concurrent.TrieMap[String, Fiber[IO, Throwable, Unit]]()
  // Add action flags to prevent multiple instances of the same process from being tracked
  private val actionFlags = scala.collection.concurrent.TrieMap[String, Ref[IO, Boolean]]()

  def startDiscovery: IO[Unit] = {
    Stream.awakeEvery[IO](5.second)
      .evalMap { _ =>
        pauseSignal.get.flatMap { isPaused =>
          if (isPaused) {
            if (debug) IO.println("Discovery paused") else IO.unit
          } else {
            discoverProcesses
          }
        }
      }
      .compile
      .drain
  }

  private def discoverProcesses: IO[Unit] = {
    db.getAllApps.flatMap { trackedApps =>
      trackedApps.traverse_ { app =>
        app.name.traverse_ { processName =>
          ProcessUtils.isProcessRunning(processName).flatMap { isRunning =>
            val isCurrentlyTracked = runningProcesses.contains(processName)

            if (isRunning && !isCurrentlyTracked) {
              // Get or create action flag for this process
              getOrCreateActionFlag(processName).flatMap { actionFlag =>
                actionFlag.get.flatMap { inProgress =>
                  IO.whenA(!inProgress) {
                    startMonitoring(processName, app, actionFlag)
                  }
                }
              }
            } else if (!isRunning && isCurrentlyTracked) {
              stopMonitoring(processName)
            } else IO.unit
          }
        }
      }
    }
  }

  private def getOrCreateActionFlag(processName: String): IO[Ref[IO, Boolean]] = {
    actionFlags.get(processName) match {
      case Some(flag) => IO.pure(flag)
      case None =>
        Ref[IO].of(false).map { newFlag =>
          actionFlags.put(processName, newFlag)
          newFlag
        }
    }
  }

  private def startMonitoring(processName: String, app: App, actionFlag: Ref[IO, Boolean]): IO[Unit] = {
    for {
      _ <- if (debug) IO.println(s"Starting to monitor: $processName") else IO.unit
      _ <- actionFlag.set(true)
      fiber <- processMonitor.monitorProcess(processName, app)
        .guarantee(actionFlag.set(false)) // Ensure flag is reset when monitoring ends
        .start
      _ = runningProcesses.put(processName, fiber)
    } yield ()
  }

  private def stopMonitoring(processName: String): IO[Unit] = {
    runningProcesses.remove(processName) match {
      case Some(fiber) =>
        if (debug) IO.println(s"Stopping monitor for: $processName") else IO.unit
      case None => IO.unit
    }
  }
}

// Simple Communication Service (replaces WebSocket for simplicity)
class CommunicationService(pauseSignal: SignallingRef[IO, Boolean], db: DatabaseOps, debug: Boolean) {

  private val commandFile = "/tmp/time-tracker-commands"

  // Simple file-based communication for pause/unpause
  def checkForCommands: IO[Unit] = {
    IO {
      Try {
        if (os.exists(os.Path(commandFile))) {
          val content = os.read(os.Path(commandFile)).trim
          os.remove(os.Path(commandFile))
          Some(content)
        } else None
      }.getOrElse(None)
    }.flatMap {
      case Some("pause") =>
        pauseSignal.get.flatMap(current => pauseSignal.set(!current)) *>
        (if (debug) IO.println("Toggled pause state") else IO.unit)
      case Some(cmd) if cmd.startsWith("insert:") =>
        val processName = cmd.substring("insert:".length)
        insertProcessCommand(processName)
      case _ => IO.unit
    }
  }

  private def insertProcessCommand(processName: String): IO[Unit] = {
    db.getAppByName(processName).flatMap {
      case Some(_) =>
        if (debug) IO.println(s"App '$processName' already exists") else IO.unit
      case None =>
        db.insertNewApp(processName) *>
        (if (debug) IO.println(s"App '$processName' has been inserted") else IO.unit)
    }
  }

  def startCommandListener: IO[Unit] = {
    Stream.awakeEvery[IO](5.second)
      .evalMap(_ => checkForCommands)
      .compile
      .drain
  }
}

// Main Application
object Main extends IOApp {

  private def createSession: Resource[IO, Session[IO]] =
    Session.single[IO](
      host = "********",
      port = 5432,
      user = "postgres",
      database = "time_tracker",
      password = Some("root")
    )

  private def displayApp(db: DatabaseOps, processName: String): IO[ExitCode] =
    db.getAppByName(processName).flatMap {
      case Some(app) =>
        IO.println(s"Time data for app: '$processName'\n") *>
        IO.println(s"ID: ${app.id}") *>
        IO.println(s"Name: ${app.name.getOrElse("N/A")}") *>
        IO.println(s"Product Name: ${app.productName.getOrElse("N/A")}") *>
        IO.println(s"Duration: ${app.duration.map(d => s"$d minutes").getOrElse("N/A")}") *>
        IO.println(s"Launches: ${app.launches.getOrElse("N/A")}") *>
        IO.println(s"Longest Session: ${app.longestSession.map(ls => s"$ls minutes").getOrElse("N/A")}") *>
        IO.println(s"Longest Session On: ${app.longestSessionOn.map(_.toString).getOrElse("N/A")}") *>
        IO.pure(ExitCode.Success)
      case None =>
        IO.println(s"Could not find App: '$processName'") *>
        IO.pure(ExitCode.Error)
    }

  private def displayAllApps(db: DatabaseOps): IO[ExitCode] =
    db.getAllApps.flatMap { apps =>
      IO.println("Time data for all apps.\n") *>
      apps.traverse_ { app =>
        IO.println(s"ID: ${app.id}") *>
        IO.println(s"Name: ${app.name.getOrElse("N/A")}") *>
        IO.println(s"Product Name: ${app.productName.getOrElse("N/A")}") *>
        IO.println(s"Duration: ${app.duration.map(d => s"$d minutes").getOrElse("N/A")}") *>
        IO.println(s"Launches: ${app.launches.getOrElse("N/A")}") *>
        IO.println(s"Longest Session: ${app.longestSession.map(ls => s"$ls minutes").getOrElse("N/A")}") *>
        IO.println(s"Longest Session On: ${app.longestSessionOn.map(_.toString).getOrElse("N/A")}") *>
        IO.println("-" * 30)
      } *> IO.pure(ExitCode.Success)
    }

  private def insertApp(db: DatabaseOps, processName: String, debug: Boolean): IO[ExitCode] =
    db.getAppByName(processName).flatMap {
      case Some(_) =>
        (if (debug) IO.println(s"App '$processName' already exists") else IO.unit) *>
        IO.pure(ExitCode.Success)
      case None =>
        db.insertNewApp(processName) *>
        (if (debug) IO.println(s"App '$processName' has been inserted") else IO.unit) *>
        IO.pure(ExitCode.Success)
    }

  private def sendPauseCommand: IO[ExitCode] = {
    IO {
      Try {
        os.write(os.Path("/tmp/time-tracker-commands"), "pause")
        println("Pause command sent")
      }.recover { case ex =>
        println(s"Failed to send pause command: ${ex.getMessage}")
      }
    } *> IO.pure(ExitCode.Success)
  }

  private def startTrackingService(db: DatabaseOps, debug: Boolean): IO[ExitCode] = {
    for {
      pauseSignal <- SignallingRef[IO, Boolean](false)

      processMonitor = new ProcessMonitor(db, pauseSignal, debug)
      communicationService = new CommunicationService(pauseSignal, db, debug)
      discoveryService = new DiscoveryService(db, processMonitor, pauseSignal, debug)

      _ <- if (debug) IO.println("Starting time tracker service...") else IO.unit

      // Start command listener
      _ <- communicationService.startCommandListener.start

      // Start discovery service
      _ <- discoveryService.startDiscovery
    } yield ExitCode.Success
  }

  def run(args: List[String]): IO[ExitCode] = {
    CaseApp.parse[Config](args) match {
      case Right((config, remaining)) =>
        createSession.use { session =>
          val db = new DatabaseOps(session)

          config match {
            case Config(_, true, _, _, _) =>
              displayAllApps(db)
            case Config(Some(processName), _, _, _, _) =>
              displayApp(db, processName)
            case Config(_, _, Some(processName), debug, _) =>
              insertApp(db, processName, debug)
            case Config(_, _, _, _, true) =>
              sendPauseCommand
            case _ =>
              startTrackingService(db, config.debug)
          }
        }
      case Left(error) =>
        IO.println(s"Error parsing arguments: ${error.message}") *>
        IO.pure(ExitCode.Error)
    }
  }
}
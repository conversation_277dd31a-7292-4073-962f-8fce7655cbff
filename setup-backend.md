# Time Tracker Backend Setup Guide

This guide explains how to set up and run the enhanced Scala backend with HTTP API support for the Flutter GUI.

## Prerequisites

1. **Scala CLI** (for running the enhanced backend)
   ```bash
   # Install Scala CLI
   curl -sSLf https://scala-cli.virtuslab.org/get | sh
   ```

2. **PostgreSQL Database**
   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install postgresql postgresql-contrib
   
   # macOS
   brew install postgresql
   brew services start postgresql
   
   # Windows
   # Download from https://www.postgresql.org/download/windows/
   ```

3. **Database Setup**
   ```sql
   -- Connect to PostgreSQL as superuser
   sudo -u postgres psql
   
   -- Create database and user
   CREATE DATABASE time_tracker;
   CREATE USER postgres WITH PASSWORD 'root';
   GRANT ALL PRIVILEGES ON DATABASE time_tracker TO postgres;
   
   -- Connect to the time_tracker database
   \c time_tracker
   
   -- Create tables
   CREATE TABLE apps (
       id SERIAL PRIMARY KEY,
       name VARCHAR(255),
       product_name VARCHAR(255),
       duration INTEGER DEFAULT 0,
       launches INTEGER DEFAULT 0,
       longest_session INTEGER DEFAULT 0,
       longest_session_on DATE
   );
   
   CREATE TABLE timeline (
       id SERIAL PRIMARY KEY,
       date DATE,
       duration INTEGER,
       app_id INTEGER REFERENCES apps(id)
   );
   
   -- Create stored procedure for timeline upsert
   CREATE OR REPLACE FUNCTION upsert_timeline(app_name VARCHAR, track_date DATE)
   RETURNS VOID AS $$
   DECLARE
       app_record apps%ROWTYPE;
   BEGIN
       -- Get the app record
       SELECT * INTO app_record FROM apps WHERE name = app_name;
       
       IF FOUND THEN
           -- Insert or update timeline entry
           INSERT INTO timeline (date, duration, app_id) 
           VALUES (track_date, 1, app_record.id)
           ON CONFLICT (date, app_id) 
           DO UPDATE SET duration = timeline.duration + 1;
       END IF;
   END;
   $$ LANGUAGE plpgsql;
   ```

## Running the Enhanced Backend

### 1. Start the HTTP API Server

```bash
# Run the enhanced backend with HTTP server
scala-cli run time-tracker-enhanced-with-api.scala -- --server --debug --port 8080
```

This will start:
- HTTP API server on port 8080
- Process monitoring and discovery
- Real-time tracking status updates
- Legacy file-based command support

### 2. Available API Endpoints

The server provides the following REST API endpoints:

#### Apps Management
- `GET /api/apps` - Get all tracked applications
- `GET /api/apps/{name}` - Get specific application by name
- `POST /api/apps` - Add new application to track
  ```json
  {"name": "application_name"}
  ```
- `DELETE /api/apps/{id}` - Remove application from tracking

#### Tracking Control
- `GET /api/tracking/status` - Get current tracking status
- `POST /api/tracking/command` - Send tracking commands
  ```json
  {"command": "start|stop|pause|resume"}
  ```

#### Data Retrieval
- `GET /api/timeline?start_date=YYYY-MM-DD&end_date=YYYY-MM-DD` - Get timeline data
- `GET /api/timeline?app_id=123` - Get timeline for specific app
- `GET /api/statistics` - Get usage statistics for all apps

### 3. Response Format

All API responses follow this format:
```json
{
  "success": true,
  "data": { ... },
  "message": "Optional message"
}
```

### 4. Testing the API

You can test the API using curl:

```bash
# Get all apps
curl http://localhost:8080/api/apps

# Get tracking status
curl http://localhost:8080/api/tracking/status

# Add a new app
curl -X POST http://localhost:8080/api/apps \
  -H "Content-Type: application/json" \
  -d '{"name": "firefox"}'

# Start tracking
curl -X POST http://localhost:8080/api/tracking/command \
  -H "Content-Type: application/json" \
  -d '{"command": "start"}'

# Get statistics
curl http://localhost:8080/api/statistics
```

## Connecting Flutter App

### 1. Update Flutter Configuration

In `time_tracker_gui/lib/presentation/providers/app_providers.dart`, change:

```dart
const useMockBackend = false; // Set to false to use real backend
```

### 2. Update API Endpoints

In `time_tracker_gui/lib/core/constants/api_constants.dart`, ensure:

```dart
static const String baseUrl = 'http://localhost:8080';
static const String wsUrl = 'ws://localhost:8081'; // For future WebSocket support
```

### 3. Run Flutter App

```bash
cd time_tracker_gui
flutter run -d linux  # or your preferred platform
```

## Database Configuration

If you need to change database connection settings, update the `createSession` method in `time-tracker-enhanced-with-api.scala`:

```scala
private def createSession: Resource[IO, Session[IO]] =
  Session.single[IO](
    host = "localhost",        // Change to your DB host
    port = 5432,              // Change to your DB port
    user = "postgres",        // Change to your DB user
    database = "time_tracker", // Change to your DB name
    password = Some("root")   // Change to your DB password
  )
```

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Ensure PostgreSQL is running
   - Check connection parameters
   - Verify database and tables exist

2. **Port Already in Use**
   - Change the port: `--port 8081`
   - Kill existing processes: `lsof -ti:8080 | xargs kill`

3. **Permission Denied for Process Monitoring**
   - Run with appropriate permissions
   - Some process monitoring features may require elevated privileges

4. **Flutter Connection Issues**
   - Ensure backend is running on correct port
   - Check firewall settings
   - Verify API endpoints are accessible

### Logs and Debugging

- Use `--debug` flag for verbose logging
- Check server console for API request logs
- Monitor database connections and queries

## Production Deployment

For production deployment:

1. **Security**: Add authentication and authorization
2. **HTTPS**: Configure SSL/TLS certificates
3. **Database**: Use connection pooling and proper credentials
4. **Monitoring**: Add health checks and metrics
5. **CORS**: Configure appropriate CORS policies for your domain

## Next Steps

1. **WebSocket Support**: Add real-time WebSocket communication for live updates
2. **Authentication**: Implement user authentication and authorization
3. **Caching**: Add Redis or similar for improved performance
4. **Monitoring**: Add application monitoring and alerting
5. **Docker**: Containerize the application for easier deployment

# Communication Architecture: Scala Backend ↔ Flutter Frontend

This document details the complete communication architecture between the enhanced Scala backend and Flutter GUI application.

## 🏗️ Architecture Overview

```
┌─────────────────┐    HTTP REST API    ┌─────────────────┐
│                 │◄──────────────────►│                 │
│  Flutter GUI    │                    │  Scala Backend  │
│  (Frontend)     │    WebSocket*       │  (HTTP4S)       │
│                 │◄──────────────────►│                 │
└─────────────────┘                    └─────────────────┘
         │                                       │
         │                                       │
         ▼                                       ▼
┌─────────────────┐                    ┌─────────────────┐
│   Riverpod      │                    │   PostgreSQL    │
│ State Management│                    │    Database     │
└─────────────────┘                    └─────────────────┘

* WebSocket support planned for future real-time updates
```

## 🔌 Communication Protocols

### 1. HTTP REST API (Primary)
- **Purpose**: CRUD operations, command execution, data retrieval
- **Format**: JSON request/response
- **Port**: 8080 (configurable)
- **CORS**: Enabled for cross-origin requests

### 2. WebSocket (Future Enhancement)
- **Purpose**: Real-time updates, live tracking status
- **Port**: 8081 (planned)
- **Events**: tracking_status_update, app_update, session_update

## 📡 API Endpoints Specification

### Apps Management

#### GET /api/apps
**Purpose**: Retrieve all tracked applications

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Visual Studio Code",
      "productName": "Code",
      "duration": 480,
      "launches": 15,
      "longestSession": 180,
      "longestSessionOn": "2024-01-15"
    }
  ]
}
```

#### GET /api/apps/{name}
**Purpose**: Get specific application by name

**Response**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "Visual Studio Code",
    "productName": "Code",
    "duration": 480,
    "launches": 15,
    "longestSession": 180,
    "longestSessionOn": "2024-01-15"
  }
}
```

#### POST /api/apps
**Purpose**: Add new application to tracking

**Request**:
```json
{
  "name": "firefox"
}
```

**Response**:
```json
{
  "success": true,
  "message": "App created successfully"
}
```

#### DELETE /api/apps/{id}
**Purpose**: Remove application from tracking

**Response**:
```json
{
  "success": true,
  "message": "App deleted successfully"
}
```

### Tracking Control

#### GET /api/tracking/status
**Purpose**: Get current tracking status

**Response**:
```json
{
  "success": true,
  "data": {
    "isTracking": true,
    "isPaused": false,
    "currentApp": "Visual Studio Code",
    "currentSessionDuration": 1800,
    "sessionStartTime": "2024-01-15T10:30:00Z"
  }
}
```

#### POST /api/tracking/command
**Purpose**: Send tracking commands

**Request**:
```json
{
  "command": "start|stop|pause|resume"
}
```

**Response**:
```json
{
  "success": true,
  "message": "Command executed successfully"
}
```

### Data Retrieval

#### GET /api/timeline
**Purpose**: Get usage timeline data

**Query Parameters**:
- `start_date`: YYYY-MM-DD format
- `end_date`: YYYY-MM-DD format  
- `app_id`: Integer app ID

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "date": "2024-01-15",
      "duration": 60,
      "appId": 1
    }
  ]
}
```

#### GET /api/statistics
**Purpose**: Get comprehensive usage statistics

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "app": {
        "id": 1,
        "name": "Visual Studio Code",
        "duration": 480
      },
      "totalDuration": 480,
      "todayDuration": 120,
      "weekDuration": 600,
      "monthDuration": 2400,
      "averageSessionLength": 45.5,
      "recentSessions": []
    }
  ]
}
```

## 🔄 Data Flow

### 1. Application Startup
```
Flutter App → GET /api/apps → Display app list
Flutter App → GET /api/tracking/status → Show tracking status
Flutter App → GET /api/statistics → Render charts
```

### 2. User Interactions
```
User clicks "Start" → POST /api/tracking/command {"command": "start"}
User adds app → POST /api/apps {"name": "new-app"}
User deletes app → DELETE /api/apps/{id}
```

### 3. Real-time Updates (Current)
```
Flutter App → Periodic GET /api/tracking/status (every 5 seconds)
Flutter App → Update UI with new status
```

### 4. Real-time Updates (Future with WebSocket)
```
Scala Backend → WebSocket: tracking_status_update
Flutter App → Receive update → Update UI immediately
```

## 🛠️ Implementation Details

### Flutter Side (Client)

#### API Service Layer
```dart
class ApiService {
  Future<List<AppModel>> getAllApps() async {
    final response = await http.get('$baseUrl/api/apps');
    final data = json.decode(response.body);
    return (data['data'] as List)
        .map((json) => AppModel.fromJson(json))
        .toList();
  }
}
```

#### State Management (Riverpod)
```dart
final appsProvider = StateNotifierProvider<AppsNotifier, AsyncValue<List<AppModel>>>((ref) {
  return AppsNotifier(ref.read(timeTrackerRepositoryProvider));
});
```

#### Repository Pattern
```dart
class TimeTrackerRepositoryImpl implements TimeTrackerRepository {
  final ApiService _apiService;
  
  @override
  Future<List<AppModel>> getAllApps() => _apiService.getAllApps();
}
```

### Scala Side (Server)

#### HTTP4S Routes
```scala
val routes: HttpRoutes[IO] = Router(
  "/api" -> HttpRoutes.of[IO] {
    case GET -> Root / "apps" =>
      db.getAllApps.flatMap { apps =>
        Ok(ApiResponse(success = true, data = Some(apps)).asJson)
      }
  }
)
```

#### Database Operations
```scala
class DatabaseOps(session: Session[IO]) {
  def getAllApps: IO[List[App]] =
    session.prepare(selectAllApps).flatMap(_.stream(Void, 1024).compile.toList)
}
```

## 🧪 Testing Communication

### 1. Backend Health Check
```bash
curl http://localhost:8080/api/apps
```

### 2. Automated Testing
```bash
dart test-communication.dart
```

### 3. Flutter Integration Test
```bash
cd time_tracker_gui
flutter test integration_test/
```

## 🔧 Configuration

### Backend Configuration
```scala
// In time-tracker-enhanced-with-api.scala
case class Config(
  port: Int = 8080,           // HTTP server port
  debug: Boolean = false,     // Enable debug logging
  server: Boolean = false     // Start HTTP server mode
)
```

### Flutter Configuration
```dart
// In lib/core/constants/api_constants.dart
class ApiConstants {
  static const String baseUrl = 'http://localhost:8080';
  static const String wsUrl = 'ws://localhost:8081';
}
```

## 🚨 Error Handling

### HTTP Status Codes
- `200`: Success
- `201`: Created (for POST requests)
- `400`: Bad Request (invalid data)
- `404`: Not Found
- `500`: Internal Server Error

### Error Response Format
```json
{
  "success": false,
  "message": "Error description"
}
```

### Flutter Error Handling
```dart
try {
  final apps = await apiService.getAllApps();
  state = AsyncValue.data(apps);
} catch (error, stackTrace) {
  state = AsyncValue.error(error, stackTrace);
}
```

## 🔮 Future Enhancements

### 1. WebSocket Implementation
- Real-time tracking status updates
- Live session duration updates
- Instant app discovery notifications

### 2. Authentication
- JWT token-based authentication
- User session management
- Role-based access control

### 3. Caching
- Redis for session storage
- Client-side caching with TTL
- Optimistic updates

### 4. Performance Optimization
- Connection pooling
- Request batching
- Compression (gzip)

## 📊 Monitoring & Debugging

### Backend Logs
```bash
# Run with debug mode
scala-cli run time-tracker-enhanced-with-api.scala -- --server --debug

# Monitor API requests
tail -f /var/log/time-tracker/api.log
```

### Flutter Debugging
```dart
// Enable HTTP logging
final client = http.Client();
client.interceptors.add(LoggingInterceptor());
```

### Network Analysis
```bash
# Monitor HTTP traffic
tcpdump -i any port 8080

# Check port availability
netstat -tulpn | grep 8080
```

This communication architecture provides a robust, scalable foundation for real-time time tracking with clear separation of concerns and comprehensive error handling.
